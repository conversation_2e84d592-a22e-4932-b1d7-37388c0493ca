// Set up environment before imports
process.env.NODE_ENV = "test";

// Mock all the database models directly
jest.mock("../scripts/DB/schemes/user/user", () => {
  const mockUserConstructor = jest.fn().mockImplementation((data) => ({
    _id: "mockUserId",
    sessionId: "mockSessionId",
    channelId: "mockChannelId",
    deviceId: "mockDeviceId",
    blockList: [],
    reportList: [],
    save: jest.fn().mockResolvedValue({}),
    toObject: jest.fn().mockReturnValue({
      _id: "mockUserId",
      sessionId: "mockSessionId",
      channelId: "mockChannelId",
      deviceId: "mockDeviceId",
      blockList: [],
      reportList: []
    }),
    ...data
  }));

  // Add static methods with proper chaining
  mockUserConstructor.findOneAndUpdate = jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue({
        _id: "mockUserId",
        sessionId: "mockSessionId",
        channelId: "mockChannelId",
        deviceId: "mockDeviceId",
        blockList: [],
        reportList: [],
        save: jest.fn().mockResolvedValue({})
      })
    })
  });

  mockUserConstructor.findOne = jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue({
      _id: "mockUserId",
      sessionId: "mockSessionId",
      channelId: "mockChannelId",
      deviceId: "mockDeviceId",
      blockList: [],
      reportList: [],
      save: jest.fn().mockResolvedValue({})
    })
  });

  mockUserConstructor.deleteOne = jest.fn().mockResolvedValue({ deletedCount: 1 });

  return mockUserConstructor;
});

// Mock other database models
jest.mock("../scripts/DB/schemes/chatting/chatting", () => ({
  findOne: jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(null)
    })
  }),
  findOneAndDelete: jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue(null)
  }),
  countDocuments: jest.fn().mockResolvedValue(0)
}));

jest.mock("../scripts/DB/schemes/waiting/waiting", () => ({
  findOne: jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(null)
    })
  }),
  findOneAndUpdate: jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(null)
    })
  }),
  deleteOne: jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue({ deletedCount: 1 })
  }),
  countDocuments: jest.fn().mockResolvedValue(0)
}));

jest.mock("../scripts/DB/schemes/banned/banned", () => ({
  findOne: jest.fn().mockResolvedValue(null),
  findOneAndUpdate: jest.fn().mockResolvedValue(null),
  deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 })
}));

jest.mock("../scripts/DB/schemes/user/settings", () => ({
  findOne: jest.fn().mockResolvedValue(null),
  findOneAndUpdate: jest.fn().mockResolvedValue(null),
  updateOne: jest.fn().mockResolvedValue({}),
  deleteMany: jest.fn().mockResolvedValue({ deletedCount: 0 }),
  aggregate: jest.fn().mockReturnValue({
    exec: jest.fn().mockImplementation((callback) => {
      if (callback) callback(null, []);
      return Promise.resolve([]);
    })
  })
}));

// Mock banned controller
jest.mock("../scripts/DB/schemes/banned/banned.ctrl", () => ({
  checkIfbanned: jest.fn().mockResolvedValue({ status: false }),
  add: jest.fn().mockResolvedValue({}),
  remove: jest.fn().mockResolvedValue({})
}));

// Mock user controller directly
jest.mock("../scripts/DB/schemes/user/user.ctrl", () => ({
  find: jest.fn().mockResolvedValue({
    sessionId: "mockSessionId",
    channelId: "mockChannelId",
    deviceId: "mockDeviceId",
    blockList: [],
    reportList: [],
    save: jest.fn().mockResolvedValue({}),
    toObject: jest.fn().mockReturnValue({
      sessionId: "mockSessionId",
      channelId: "mockChannelId",
      deviceId: "mockDeviceId",
      blockList: [],
      reportList: []
    })
  }),
  create: jest.fn().mockResolvedValue({
    sessionId: "mockSessionId",
    channelId: "mockChannelId",
    deviceId: "mockDeviceId",
    blockList: [],
    reportList: [],
    save: jest.fn().mockResolvedValue({}),
    toObject: jest.fn().mockReturnValue({
      sessionId: "mockSessionId",
      channelId: "mockChannelId",
      deviceId: "mockDeviceId",
      blockList: [],
      reportList: []
    })
  })
}));

// Mock other controllers
jest.mock("../scripts/DB/schemes/chatting/chatting.ctrl", () => ({
  find: jest.fn().mockResolvedValue(null),
  create: jest.fn().mockResolvedValue({}),
  remove: jest.fn().mockResolvedValue(null)
}));

jest.mock("../scripts/DB/schemes/waiting/waiting.ctrl", () => ({
  add: jest.fn().mockResolvedValue({}),
  remove: jest.fn().mockResolvedValue({}),
  fetch: jest.fn().mockResolvedValue(null)
}));

// Mock dependencies
jest.mock("../scripts/DB/dataAccess");
jest.mock("../scripts/responseHandler");

const { replyBack } = require("../scripts/requestHandler");
const dataAccess = require("../scripts/DB/dataAccess");
const responseHandler = require("../scripts/responseHandler");
const messages = require("../scripts/responseMessages");

describe("Request Handler", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Set up mocks in beforeEach to ensure they're applied correctly
    const UserController = require("../scripts/DB/schemes/user/user.ctrl");
    const BannedController = require("../scripts/DB/schemes/banned/banned.ctrl");
    const WaitingController = require("../scripts/DB/schemes/waiting/waiting.ctrl");

    UserController.find = jest.fn().mockResolvedValue({
      sessionId: "mockSessionId",
      channelId: "mockChannelId",
      deviceId: "mockDeviceId",
      blockList: [],
      reportList: [],
      save: jest.fn().mockResolvedValue({}),
      toObject: jest.fn().mockReturnValue({
        sessionId: "mockSessionId",
        channelId: "mockChannelId",
        deviceId: "mockDeviceId",
        blockList: [],
        reportList: []
      })
    });

    BannedController.checkIfbanned = jest.fn().mockResolvedValue({ status: false });

    // Mock WaitingController methods
    WaitingController.remove = jest.fn().mockResolvedValue({});
    WaitingController.add = jest.fn().mockResolvedValue({});
    WaitingController.fetch = jest.fn().mockResolvedValue(null);

    // Mock dataAccess methods
    dataAccess.getReceiver = jest.fn().mockResolvedValue({
      sessionId: "mockSessionId",
      channelId: "mockChannelId",
      deviceId: "mockDeviceId"
    });

    dataAccess.removeFromChatting = jest.fn().mockResolvedValue({
      participants: [
        { sessionId: "user1", deviceId: "device1" },
        { sessionId: "user2", deviceId: "device2" }
      ]
    });

    dataAccess.addUserToWaiting = jest.fn().mockResolvedValue({ user: {} });
    dataAccess.removeFromWaiting = jest.fn().mockResolvedValue({});

    // Mock responseHandler
    responseHandler.handleMessage = jest.fn();
  });

  describe("replyBack", () => {
    it("should handle postback messages", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        postback: { payload: "RECONNECT" }
      };

      // Mock the chatting data with participants for RECONNECT
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue({
        participants: [
          { sessionId: "user123", deviceId: "device123" },
          { sessionId: "user456", deviceId: "device456" }
        ]
      });
      dataAccess.addUserToWaiting = jest.fn().mockResolvedValue({ user: { sessionId: "user123" } });
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();
    });

    it("should handle text messages", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockReceiver = { sessionId: "user456", channelId: "channel456" };
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();
    });

    it("should handle same user messaging (waiting scenario)", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockReceiver = { sessionId: "user123", channelId: "channel456" };
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();

      // Check if any call contains a waiting message
      const calls = responseHandler.handleMessage.mock.calls;
      const hasWaitingMessage = calls.some(call => {
        const user = call[0];
        const message = call[1];
        return user && user.sessionId === "user123" && message && typeof message.text === 'string';
      });

      expect(hasWaitingMessage).toBe(true);
    });

    it("should handle newly matched users", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockReceiver = {
        sessionId: "user456",
        channelId: "channel456",
        justMatched: true
      };
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();

      // Check if any call contains the "now connected" message
      const calls = responseHandler.handleMessage.mock.calls;
      const hasConnectedMessage = calls.some(call => {
        const message = call[1];
        return message && message.text === messages.nowConnected;
      });

      expect(hasConnectedMessage).toBe(true);
    });
  });

  describe("Postback handling", () => {
    it("should handle DISCONNECT postback", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        postback: { payload: "DISCONNECT" }
      };

      dataAccess.removeFromChatting = jest.fn().mockResolvedValue({
        participants: [
          { sessionId: "user123", deviceId: "device123" },
          { sessionId: "user456", deviceId: "device456" }
        ]
      });
      dataAccess.removeFromWaiting = jest.fn().mockResolvedValue({});
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();
    });

    it("should handle RECONNECT postback", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        postback: { payload: "RECONNECT" }
      };

      dataAccess.removeFromChatting = jest.fn().mockResolvedValue({
        participants: [
          { sessionId: "user123", deviceId: "device123" },
          { sessionId: "user456", deviceId: "device456" }
        ]
      });
      dataAccess.addUserToWaiting = jest.fn().mockResolvedValue({ user: { sessionId: "user123" } });
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();
    });

    it("should handle help postback", async () => {
      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        postback: { payload: "GET_STARTED" }
      };

      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called with help message
      expect(responseHandler.handleMessage).toHaveBeenCalled();

      // Check if any call contains the help message
      const calls = responseHandler.handleMessage.mock.calls;
      const hasHelpMessage = calls.some(call => {
        const message = call[1];
        return message && message.text === messages.help;
      });

      expect(hasHelpMessage).toBe(true);
    });

    it("should handle chatting session with participants", async () => {
      const mockSender = { sessionId: "user123", channelId: "channel456" };
      const mockOtherUser = { sessionId: "user456", channelId: "channel456" };
      const mockChattingData = {
        participants: [mockSender, mockOtherUser]
      };

      const mockBody = {
        sender: {
          id: "user123",
          sessionId: "user123",
          channelId: "channel456",
          deviceId: "device123"
        },
        recipient: { id: "channel456" },
        postback: { payload: "DISCONNECT" }
      };

      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockChattingData);
      dataAccess.removeFromWaiting = jest.fn().mockResolvedValue({});
      responseHandler.handleMessage = jest.fn();

      replyBack(mockBody);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();

      // Check if any call contains the partner left message
      const calls = responseHandler.handleMessage.mock.calls;
      const hasPartnerLeftMessage = calls.some(call => {
        const message = call[1];
        return message && message.text === messages.partnerLeft;
      });

      expect(hasPartnerLeftMessage).toBe(true);
    });
  });
});
