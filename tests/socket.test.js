const Client = require("socket.io-client");
const { server, images } = require("../app.socket.io");
const { replyBack, checkIfbanned } = require("../scripts/requestHandler");
const { uploadImg } = require("../google.config");

// Mock dependencies
jest.mock("../scripts/requestHandler");
jest.mock("../google.config");

describe("Socket.IO Functionality", () => {
  let clientSocket;
  let httpServer;

  beforeAll((done) => {
    jest.setTimeout(15000); // Increase timeout for socket setup

    httpServer = server.listen(() => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        timeout: 5000,
        forceNew: true,
        query: {
          deviceId: "test-device-123",
          channelId: "test-channel-456"
        }
      });

      server.on("connection", () => {
        done(); // Call done when server socket is ready
      });

      clientSocket.on("connect", () => {
        console.log("Client socket connected");
      });

      clientSocket.on("connect_error", (error) => {
        console.error("Client socket connection error:", error);
        done(error);
      });
    });
  });

  afterAll((done) => {
    if (clientSocket) {
      clientSocket.close();
    }
    if (httpServer) {
      httpServer.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock checkIfbanned to return false (not banned)
    checkIfbanned.mockResolvedValue(false);
    // Clear images array between tests
    Object.keys(images).forEach(key => delete images[key]);
    // Remove all event listeners to prevent interference between tests
    if (clientSocket) {
      clientSocket.removeAllListeners("ack");
    }
  });

  describe("Chat Message Events", () => {
    it("should handle chat message event", (done) => {
      const testMessage = {
        id: "msg123",
        text: "Hello world"
      };

      // Set a timeout to prevent hanging
      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for ack"));
      }, 4000);

      replyBack.mockImplementation((msg) => {
        try {
          // The actual implementation creates a sender object with sessionId, channelId, deviceId
          expect(msg.sender).toBeDefined();
          expect(msg.sender.sessionId).toBe(clientSocket.id);
          expect(msg.message).toEqual({ text: "Hello world" });
        } catch (error) {
          clearTimeout(timeout);
          done(error);
        }
      });

      clientSocket.on("ack", (ack) => {
        try {
          clearTimeout(timeout);
          expect(ack).toEqual({ id: "msg123" });
          done();
        } catch (error) {
          done(error);
        }
      });

      // Ensure socket is connected before emitting
      if (clientSocket.connected) {
        clientSocket.emit("chat message", testMessage);
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("chat message", testMessage);
        });
      }
    });

    it("should handle empty chat message", (done) => {
      const testMessage = {
        id: "msg123",
        text: ""
      };

      replyBack.mockImplementation((msg) => {
        try {
          expect(msg.message).toBeUndefined();
          done();
        } catch (error) {
          done(error);
        }
      });

      if (clientSocket.connected) {
        clientSocket.emit("chat message", testMessage);
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("chat message", testMessage);
        });
      }
    });
  });

  describe("Image Upload Events", () => {
    it("should handle image chunks", (done) => {
      const imageData = {
        id: "img123",
        index: 1,
        data: "base64imagedata"
      };

      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for image response"));
      }, 5000);

      if (clientSocket.connected) {
        clientSocket.emit("image", imageData, (response) => {
          try {
            clearTimeout(timeout);
            expect(response).toBe(1);
            done();
          } catch (error) {
            done(error);
          }
        });
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("image", imageData, (response) => {
            try {
              clearTimeout(timeout);
              expect(response).toBe(1);
              done();
            } catch (error) {
              done(error);
            }
          });
        });
      }
    });

    it("should accumulate image chunks", (done) => {
      const imageData1 = {
        id: "img123",
        index: 1,
        data: "base64part1"
      };

      const imageData2 = {
        id: "img123",
        index: 2,
        data: "base64part2"
      };

      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for image chunks"));
      }, 5000);

      let callCount = 0;
      const checkComplete = () => {
        callCount++;
        if (callCount === 2) {
          clearTimeout(timeout);
          done();
        }
      };

      const emitImages = () => {
        clientSocket.emit("image", imageData1, (response) => {
          try {
            expect(response).toBe(1);
            checkComplete();
          } catch (error) {
            clearTimeout(timeout);
            done(error);
          }
        });

        clientSocket.emit("image", imageData2, (response) => {
          try {
            expect(response).toBe(2);
            checkComplete();
          } catch (error) {
            clearTimeout(timeout);
            done(error);
          }
        });
      };

      if (clientSocket.connected) {
        emitImages();
      } else {
        clientSocket.on("connect", emitImages);
      }
    });

    it("should handle image sent event", (done) => {
      const imageData = {
        id: "img123",
        index: 1,
        data: "base64imagedata"
      };

      const imageSentData = {
        id: "img123"
      };

      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for image sent"));
      }, 5000);

      uploadImg.mockResolvedValue("https://example.com/uploaded-image.png");
      replyBack.mockImplementation((msg) => {
        try {
          // Check the actual structure returned by createAttachmentMsg
          expect(msg.sender).toBeDefined();
          expect(msg.sender.sessionId).toBe(clientSocket.id);
          expect(msg.message).toEqual({
            attachments: [{
              type: "image",
              payload: { url: "https://example.com/uploaded-image.png" }
            }]
          });
        } catch (error) {
          clearTimeout(timeout);
          done(error);
        }
      });

      const emitImageSequence = () => {
        // First send image data
        clientSocket.emit("image", imageData, () => {
          // Then send image sent event
          clientSocket.emit("imageSent", imageSentData, (response) => {
            try {
              clearTimeout(timeout);
              expect(response).toBe("image recieved");
              expect(uploadImg).toHaveBeenCalledWith("base64imagedata", "test-device-123", "img123.png");
              done();
            } catch (error) {
              done(error);
            }
          });
        });
      };

      if (clientSocket.connected) {
        emitImageSequence();
      } else {
        clientSocket.on("connect", emitImageSequence);
      }
    });

    it("should handle image upload failure", (done) => {
      const imageData = {
        id: "img123",
        index: 1,
        data: "base64imagedata"
      };

      const imageSentData = {
        id: "img123"
      };

      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for image upload failure"));
      }, 5000);

      uploadImg.mockRejectedValue(new Error("Upload failed"));

      // Don't expect ack event for upload failure, just check the response
      const emitImageSequence = () => {
        // First send image data
        clientSocket.emit("image", imageData, () => {
          // Then send image sent event
          clientSocket.emit("imageSent", imageSentData, (response) => {
            try {
              clearTimeout(timeout);
              expect(response).toBe("image recieved");
              expect(uploadImg).toHaveBeenCalled();
              done();
            } catch (error) {
              done(error);
            }
          });
        });
      };

      if (clientSocket.connected) {
        emitImageSequence();
      } else {
        clientSocket.on("connect", emitImageSequence);
      }
    });
  });

  describe("Connection Events", () => {
    it("should handle disconnect event", (done) => {
      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for disconnect"));
      }, 3000);

      replyBack.mockImplementation((msg) => {
        try {
          clearTimeout(timeout);
          expect(msg.sender).toBeDefined();
          expect(msg.sender.sessionId).toBeDefined();
          expect(msg.sender.deviceId).toBe("test-device-123");
          expect(msg.sender.channelId).toBe("test-channel-456");
          expect(msg.postback).toEqual({ payload: "DISCONNECT" });
          done();
        } catch (error) {
          done(error);
        }
      });

      clientSocket.disconnect();
    });

    it("should handle logout event", (done) => {
      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for logout"));
      }, 3000);

      replyBack.mockImplementation((msg) => {
        try {
          clearTimeout(timeout);
          expect(msg.sender).toBeDefined();
          expect(msg.sender.sessionId).toBe(clientSocket.id);
          expect(msg.sender.deviceId).toBe("test-device-123");
          expect(msg.sender.channelId).toBe("test-channel-456");
          expect(msg.postback).toEqual({ payload: "DISCONNECT" });
          done();
        } catch (error) {
          done(error);
        }
      });

      if (clientSocket.connected) {
        clientSocket.emit("logout", "user initiated");
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("logout", "user initiated");
        });
      }
    });

    it("should handle reconnect event", (done) => {
      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for reconnect"));
      }, 3000);

      replyBack.mockImplementation((msg) => {
        try {
          clearTimeout(timeout);
          expect(msg.sender).toBeDefined();
          expect(msg.sender.sessionId).toBe(clientSocket.id);
          expect(msg.sender.deviceId).toBe("test-device-123");
          expect(msg.sender.channelId).toBe("test-channel-456");
          expect(msg.postback).toEqual({ payload: "RECONNECT" });
          done();
        } catch (error) {
          done(error);
        }
      });

      if (clientSocket.connected) {
        clientSocket.emit("reconnect", "connection restored");
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("reconnect", "connection restored");
        });
      }
    });

    it("should handle error event", (done) => {
      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for error"));
      }, 3000);

      replyBack.mockImplementation((msg) => {
        try {
          clearTimeout(timeout);
          expect(msg.sender).toBeDefined();
          expect(msg.sender.sessionId).toBe(clientSocket.id);
          expect(msg.sender.deviceId).toBe("test-device-123");
          expect(msg.sender.channelId).toBe("test-channel-456");
          expect(msg.postback).toEqual({ payload: "RECONNECT" });
          done();
        } catch (error) {
          done(error);
        }
      });

      if (clientSocket.connected) {
        clientSocket.emit("error", "connection error");
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("error", "connection error");
        });
      }
    });
  });


});
